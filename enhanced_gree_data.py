#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版格力电器数据获取
在基础数据基础上补充更多字段
"""

from xtquant import xtdata
import pandas as pd
from datetime import datetime

def get_enhanced_gree_data():
    """获取格力电器增强数据"""
    
    # 格力电器股票代码
    stock_code = "000651.SZ"
    start_date = "20250128"
    end_date = "20250705"
    
    print(f"开始获取格力电器({stock_code})增强数据...")
    print(f"时间范围: {start_date} - {end_date}")
    print("=" * 50)
    
    try:
        # 1. 获取基础行情数据
        print("1. 获取基础行情数据...")
        field_list = ['open', 'high', 'low', 'close', 'preClose', 'volume', 'amount', 'turn', 'pctChg']
        basic_data = xtdata.get_market_data_ex(field_list, [stock_code], period='1d', 
                                             start_time=start_date, end_time=end_date)
        
        if not basic_data or stock_code not in basic_data:
            print("   未获取到指定时间范围的数据，尝试获取所有可用数据...")
            basic_data = xtdata.get_market_data_ex(field_list, [stock_code], period='1d')
            if basic_data and stock_code in basic_data:
                stock_data = basic_data[stock_code]
                # 筛选指定时间范围
                start_dt = pd.to_datetime(start_date, format='%Y%m%d')
                end_dt = pd.to_datetime(end_date, format='%Y%m%d')
                stock_data = stock_data[(stock_data.index >= start_dt) & (stock_data.index <= end_dt)]
            else:
                print("   未获取到任何行情数据")
                return None
        else:
            stock_data = basic_data[stock_code]
        
        print(f"   成功获取 {len(stock_data)} 条行情记录")
        
        # 2. 获取股票详细信息
        print("2. 获取股票详细信息...")
        try:
            instrument_detail = xtdata.get_instrument_detail(stock_code)
            if instrument_detail:
                stock_name = instrument_detail.get('InstrumentName', '格力电器')
                total_shares = instrument_detail.get('TotalShares', 0)  # 总股本
                float_shares = instrument_detail.get('FloatShares', 0)  # 流通股本
                print(f"   股票名称: {stock_name}")
                print(f"   总股本: {total_shares:,.0f}")
                print(f"   流通股本: {float_shares:,.0f}")
            else:
                stock_name = '格力电器'
                total_shares = float_shares = 0
        except Exception as e:
            print(f"   获取股票详细信息失败: {e}")
            stock_name = '格力电器'
            total_shares = float_shares = 0
        
        # 3. 获取指数成分股信息
        print("3. 获取指数成分股信息...")
        index_components = {}
        
        # 尝试不同的指数名称格式
        index_variations = {
            '沪深300': ['沪深300', 'HS300', '000300.SH'],
            '上证50': ['上证50', 'SZ50', '000016.SH'],
            '中证500': ['中证500', 'ZZ500', '000905.SH'],
            '中证1000': ['中证1000', 'ZZ1000', '000852.SH'],
            '创业板指': ['创业板指', 'CYBZ', '399006.SZ']
        }
        
        for idx_name, variations in index_variations.items():
            found = False
            for var in variations:
                try:
                    components = xtdata.get_stock_list_in_sector(var)
                    if components and len(components) > 0:
                        index_components[idx_name] = stock_code in components
                        print(f"   {idx_name}: {'是' if index_components[idx_name] else '否'} (使用{var})")
                        found = True
                        break
                except:
                    continue
            
            if not found:
                index_components[idx_name] = False
                print(f"   {idx_name}: 未获取到成分股信息")
        
        # 4. 获取行业分类信息
        print("4. 获取行业分类信息...")
        try:
            if instrument_detail:
                # 尝试不同的行业字段名
                industry_fields = [
                    ('IndustryName1', 'IndustryName2', 'IndustryName3'),
                    ('Industry1', 'Industry2', 'Industry3'),
                    ('SWIndustry1', 'SWIndustry2', 'SWIndustry3'),
                    ('sw_industry_l1', 'sw_industry_l2', 'sw_industry_l3')
                ]
                
                sw_industry_l1 = sw_industry_l2 = sw_industry_l3 = ''
                
                for field_set in industry_fields:
                    l1 = instrument_detail.get(field_set[0], '')
                    l2 = instrument_detail.get(field_set[1], '')
                    l3 = instrument_detail.get(field_set[2], '')
                    
                    if l1:  # 如果找到了一级行业
                        sw_industry_l1, sw_industry_l2, sw_industry_l3 = l1, l2, l3
                        print(f"   行业分类: {l1} > {l2} > {l3}")
                        break
                
                if not sw_industry_l1:
                    print("   未找到行业分类信息")
                    # 手动设置格力电器的行业信息
                    sw_industry_l1 = '家用电器'
                    sw_industry_l2 = '白色家电'
                    sw_industry_l3 = '空调'
                    print(f"   使用默认行业分类: {sw_industry_l1} > {sw_industry_l2} > {sw_industry_l3}")
            else:
                sw_industry_l1 = '家用电器'
                sw_industry_l2 = '白色家电'
                sw_industry_l3 = '空调'
        except Exception as e:
            print(f"   获取行业信息失败: {e}")
            sw_industry_l1 = '家用电器'
            sw_industry_l2 = '白色家电'
            sw_industry_l3 = '空调'
        
        # 5. 尝试获取财务数据
        print("5. 尝试获取财务数据...")
        try:
            # 先下载财务数据
            xtdata.download_financial_data([stock_code])
            financial_data = xtdata.get_financial_data([stock_code], ['Balance', 'Income', 'CashFlow'])
            if financial_data:
                print("   财务数据获取成功")
            else:
                print("   财务数据为空")
                financial_data = None
        except Exception as e:
            print(f"   财务数据获取失败: {e}")
            financial_data = None
        
        # 6. 整合数据
        print("6. 整合数据...")
        result_data = []
        
        for trade_date, row in stock_data.iterrows():
            # 处理日期格式
            if isinstance(trade_date, str):
                date_str = trade_date
            else:
                date_str = trade_date.strftime('%Y%m%d')
            
            # 计算前收盘价
            pre_close = row.get('preClose', 0)
            if pre_close == 0 and len(result_data) > 0:
                pre_close = result_data[-1]['收盘价']
            
            # 计算市值
            close_price = row.get('close', 0)
            total_market_value = total_shares * close_price if total_shares > 0 else 0
            float_market_value = float_shares * close_price if float_shares > 0 else 0
            
            data_row = {
                '股票代码': stock_code,
                '股票名称': stock_name,
                '交易日期': date_str,
                '开盘价': round(row.get('open', 0), 2),
                '最高价': round(row.get('high', 0), 2),
                '最低价': round(row.get('low', 0), 2),
                '收盘价': round(close_price, 2),
                '前收盘价': round(pre_close, 2),
                '成交量': int(row.get('volume', 0)),
                '成交额': round(row.get('amount', 0), 2),
                '流通市值': round(float_market_value, 2),
                '总市值': round(total_market_value, 2),
                '净利润TTM': 0,  # 财务数据需要进一步处理
                '现金流TTM': 0,
                '净资产': 0,
                '总资产': 0,
                '总负债': 0,
                '净利润(当季)': 0,
                '中户资金买入额': 0,  # 资金流向数据需要特殊权限
                '中户资金卖出额': 0,
                '大户资金买入额': 0,
                '大户资金卖出额': 0,
                '散户资金买入额': 0,
                '散户资金卖出额': 0,
                '机构资金买入额': 0,
                '机构资金卖出额': 0,
                '沪深300成分股': 1 if index_components.get('沪深300', False) else 0,
                '上证50成分股': 1 if index_components.get('上证50', False) else 0,
                '中证500成分股': 1 if index_components.get('中证500', False) else 0,
                '中证1000成分股': 1 if index_components.get('中证1000', False) else 0,
                '中证2000成分股': 0,  # 暂时设为0
                '创业板指成分股': 1 if index_components.get('创业板指', False) else 0,
                '新版申万一级行业名称': sw_industry_l1,
                '新版申万二级行业名称': sw_industry_l2,
                '新版申万三级行业名称': sw_industry_l3,
                '09:35收盘价': 0,  # 分时数据需要特殊处理
                '09:45收盘价': 0,
                '09:55收盘价': 0,
            }
            
            result_data.append(data_row)
        
        # 7. 保存数据
        df = pd.DataFrame(result_data)
        output_file = f"格力电器_增强数据_{start_date}_{end_date}.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print("=" * 50)
        print(f"增强数据获取完成！")
        print(f"共获取 {len(df)} 条记录")
        print(f"数据已保存到: {output_file}")
        
        # 显示数据概览
        print(f"\n数据概览:")
        print(f"时间范围: {df['交易日期'].min()} - {df['交易日期'].max()}")
        print(f"价格范围: {df['收盘价'].min():.2f} - {df['收盘价'].max():.2f}")
        print(f"平均成交量: {df['成交量'].mean():,.0f}")
        print(f"平均成交额: {df['成交额'].mean():,.0f}")
        if df['总市值'].max() > 0:
            print(f"市值范围: {df['总市值'].min():,.0f} - {df['总市值'].max():,.0f}")
        
        # 显示指数成分股信息
        print(f"\n指数成分股信息:")
        for idx in ['沪深300', '上证50', '中证500', '中证1000', '创业板指']:
            is_component = df[f'{idx}成分股'].iloc[0] if len(df) > 0 else 0
            print(f"  {idx}: {'是' if is_component else '否'}")
        
        # 显示前几行数据
        print("\n前3行数据预览:")
        display_cols = ['股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '成交量', '总市值']
        print(df[display_cols].head(3).to_string(index=False))
        
        return df
        
    except Exception as e:
        print(f"获取数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("=" * 60)
    print("格力电器增强数据获取工具")
    print("=" * 60)
    print("说明：在基础行情数据基础上补充市值、指数成分股等信息")
    print("=" * 60)
    print()
    
    # 执行数据获取
    data = get_enhanced_gree_data()
    
    if data is not None:
        print("\n" + "=" * 60)
        print("增强数据获取成功！")
        print("注意：财务数据、资金流向、分时数据等字段需要特殊权限或额外配置")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("数据获取失败！")
        print("=" * 60)
