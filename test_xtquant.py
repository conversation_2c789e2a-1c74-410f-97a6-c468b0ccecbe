#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试xtquant连接和基本功能
"""

try:
    import xtquant as xt
    print("xtquant导入成功")
    print(f"xtquant版本: {getattr(xt, '__version__', '未知')}")

    # 查看xtquant的所有属性和方法
    print("\nxtquant可用的属性和方法:")
    attrs = [attr for attr in dir(xt) if not attr.startswith('_')]
    for attr in sorted(attrs):
        print(f"  {attr}")

    # 尝试连接
    print("\n尝试连接...")

    # 测试获取股票基本信息
    stock_code = "000651.SZ"  # 格力电器
    print(f"测试股票代码: {stock_code}")

    # 尝试不同的API函数名
    api_functions = [
        'get_market_data', 'get_kline_data', 'get_daily_data',
        'get_stock_data', 'get_history_data', 'get_price_data'
    ]

    for func_name in api_functions:
        if hasattr(xt, func_name):
            print(f"找到函数: {func_name}")
            try:
                func = getattr(xt, func_name)
                print(f"  函数文档: {func.__doc__}")
            except:
                pass

    # 尝试获取股票列表的不同方法
    list_functions = [
        'get_stock_list_in_sector', 'get_stock_list', 'get_all_stocks',
        'get_sector_stocks', 'get_instrument_list'
    ]

    for func_name in list_functions:
        if hasattr(xt, func_name):
            print(f"找到列表函数: {func_name}")
            try:
                func = getattr(xt, func_name)
                print(f"  函数文档: {func.__doc__}")
            except:
                pass
        
except ImportError as e:
    print(f"xtquant导入失败: {e}")
    print("请确保已正确安装xtquant")
except Exception as e:
    print(f"发生错误: {e}")
    import traceback
    traceback.print_exc()
