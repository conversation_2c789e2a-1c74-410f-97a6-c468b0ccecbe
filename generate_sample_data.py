#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成格力电器样本数据
用于演示最终数据格式
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def generate_sample_gree_data():
    """生成格力电器样本数据"""
    
    # 生成交易日期（排除周末）
    start_date = datetime(2025, 1, 28)
    end_date = datetime(2025, 7, 5)
    
    trade_dates = []
    current_date = start_date
    while current_date <= end_date:
        # 排除周末
        if current_date.weekday() < 5:  # 0-4 是周一到周五
            trade_dates.append(current_date)
        current_date += timedelta(days=1)
    
    print(f"生成 {len(trade_dates)} 个交易日的样本数据")
    
    # 基础价格参数
    base_price = 35.50  # 格力电器大概价格
    
    result_data = []
    
    for i, trade_date in enumerate(trade_dates):
        # 模拟价格波动
        price_change = random.uniform(-0.05, 0.05)  # ±5%的随机波动
        close_price = base_price * (1 + price_change)
        
        # 生成OHLC数据
        high_price = close_price * random.uniform(1.0, 1.03)
        low_price = close_price * random.uniform(0.97, 1.0)
        open_price = close_price * random.uniform(0.98, 1.02)
        pre_close = base_price if i == 0 else result_data[i-1]['收盘价']
        
        # 生成成交量和成交额
        volume = random.randint(50000000, 200000000)  # 5千万到2亿股
        amount = volume * close_price
        
        # 模拟市值（基于股本和价格）
        total_shares = 6000000000  # 格力电器总股本约60亿股
        circulating_shares = 5500000000  # 流通股本约55亿股
        total_market_value = total_shares * close_price
        circulating_market_value = circulating_shares * close_price
        
        # 模拟财务数据（相对稳定）
        net_profit_ttm = random.uniform(22000000000, 25000000000)  # 净利润TTM 220-250亿
        cash_flow_ttm = random.uniform(20000000000, 30000000000)   # 现金流TTM 200-300亿
        net_assets = random.uniform(150000000000, 160000000000)    # 净资产 1500-1600亿
        total_assets = random.uniform(300000000000, 320000000000)  # 总资产 3000-3200亿
        total_liabilities = total_assets - net_assets              # 总负债
        net_profit_quarter = net_profit_ttm / 4 * random.uniform(0.8, 1.2)  # 当季净利润
        
        # 模拟资金流向数据
        total_amount_flow = amount * random.uniform(0.8, 1.2)
        retail_buy = total_amount_flow * random.uniform(0.3, 0.5)
        retail_sell = total_amount_flow * random.uniform(0.3, 0.5)
        medium_buy = total_amount_flow * random.uniform(0.15, 0.25)
        medium_sell = total_amount_flow * random.uniform(0.15, 0.25)
        large_buy = total_amount_flow * random.uniform(0.1, 0.2)
        large_sell = total_amount_flow * random.uniform(0.1, 0.2)
        institution_buy = total_amount_flow * random.uniform(0.05, 0.15)
        institution_sell = total_amount_flow * random.uniform(0.05, 0.15)
        
        # 模拟分时价格
        price_935 = open_price * random.uniform(0.995, 1.005)
        price_945 = price_935 * random.uniform(0.995, 1.005)
        price_955 = price_945 * random.uniform(0.995, 1.005)
        
        data_row = {
            '股票代码': '000651.SZ',
            '股票名称': '格力电器',
            '交易日期': trade_date.strftime('%Y%m%d'),
            '开盘价': round(open_price, 2),
            '最高价': round(high_price, 2),
            '最低价': round(low_price, 2),
            '收盘价': round(close_price, 2),
            '前收盘价': round(pre_close, 2),
            '成交量': volume,
            '成交额': round(amount, 2),
            '流通市值': round(circulating_market_value, 2),
            '总市值': round(total_market_value, 2),
            '净利润TTM': round(net_profit_ttm, 2),
            '现金流TTM': round(cash_flow_ttm, 2),
            '净资产': round(net_assets, 2),
            '总资产': round(total_assets, 2),
            '总负债': round(total_liabilities, 2),
            '净利润(当季)': round(net_profit_quarter, 2),
            '中户资金买入额': round(medium_buy, 2),
            '中户资金卖出额': round(medium_sell, 2),
            '大户资金买入额': round(large_buy, 2),
            '大户资金卖出额': round(large_sell, 2),
            '散户资金买入额': round(retail_buy, 2),
            '散户资金卖出额': round(retail_sell, 2),
            '机构资金买入额': round(institution_buy, 2),
            '机构资金卖出额': round(institution_sell, 2),
            '沪深300成分股': 1,  # 格力电器是沪深300成分股
            '上证50成分股': 0,
            '中证500成分股': 0,
            '中证1000成分股': 0,
            '中证2000成分股': 0,
            '创业板指成分股': 0,
            '新版申万一级行业名称': '家用电器',
            '新版申万二级行业名称': '白色家电',
            '新版申万三级行业名称': '空调',
            '09:35收盘价': round(price_935, 2),
            '09:45收盘价': round(price_945, 2),
            '09:55收盘价': round(price_955, 2),
        }
        
        result_data.append(data_row)
        base_price = close_price  # 更新基础价格
    
    return pd.DataFrame(result_data)

if __name__ == "__main__":
    print("=" * 60)
    print("格力电器样本数据生成工具")
    print("=" * 60)
    print("注意：这是模拟数据，仅用于演示数据格式")
    print("实际数据请使用xtquant从MiniQMT获取")
    print()
    
    # 生成样本数据
    df = generate_sample_gree_data()
    
    # 保存到CSV文件
    output_file = "格力电器_样本数据_20250128_20250705.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"样本数据已保存到: {output_file}")
    print(f"共生成 {len(df)} 条记录")
    print()
    
    # 显示数据统计
    print("数据概览:")
    print(f"时间范围: {df['交易日期'].min()} - {df['交易日期'].max()}")
    print(f"价格范围: {df['收盘价'].min():.2f} - {df['收盘价'].max():.2f}")
    print(f"平均成交量: {df['成交量'].mean():,.0f}")
    print(f"平均成交额: {df['成交额'].mean():,.0f}")
    print()
    
    # 显示前5行数据
    print("前5行数据预览:")
    print(df.head().to_string())
    print()
    
    # 显示数据字段
    print("数据字段列表:")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    print()
    print("=" * 60)
    print("样本数据生成完成！")
    print("请参考 README_xtquant使用说明.md 了解如何获取真实数据")
    print("=" * 60)
