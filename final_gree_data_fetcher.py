#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
格力电器完整数据获取脚本
需要MiniQMT客户端正常运行
"""

from xtquant import xtdata
import pandas as pd
from datetime import datetime
import time

def check_connection():
    """检查xtdata连接状态"""
    try:
        # 尝试简单的数据获取来验证连接
        test_data = xtdata.get_stock_list_in_sector('沪深A股')
        if test_data and len(test_data) > 0:
            print("xtdata连接正常，可以获取数据")
            return True
        else:
            print("连接可能有问题，未获取到股票列表")
            return False
    except Exception as e:
        print(f"连接检查失败: {e}")
        print("尝试跳过连接检查，直接获取数据...")
        return True  # 即使检查失败也继续尝试

def get_complete_gree_data():
    """获取格力电器完整数据"""
    
    # 格力电器股票代码
    stock_code = "000651.SZ"
    start_date = "20250128"
    end_date = "20250705"
    
    print(f"开始获取格力电器({stock_code})完整数据...")
    print(f"时间范围: {start_date} - {end_date}")
    print("=" * 50)
    
    # 检查连接
    print("检查连接状态...")
    connection_ok = check_connection()
    if connection_ok:
        print("连接检查通过，开始获取数据")
    else:
        print("连接检查未通过，但仍尝试获取数据")
    
    try:
        # 1. 下载基础数据
        print("1. 下载历史行情数据...")
        xtdata.download_history_data(stock_code, period='1d', start_time=start_date, end_time=end_date)
        
        print("2. 下载财务数据...")
        xtdata.download_financial_data([stock_code])
        
        print("3. 下载板块数据...")
        xtdata.download_sector_data()
        
        # 2. 获取基础行情数据
        print("4. 获取基础行情数据...")
        field_list = ['open', 'high', 'low', 'close', 'preClose', 'volume', 'amount']
        basic_data = xtdata.get_market_data_ex(field_list, [stock_code], period='1d', 
                                             start_time=start_date, end_time=end_date)
        
        if not basic_data or stock_code not in basic_data:
            print("未获取到基础行情数据")
            return None
        
        stock_data = basic_data[stock_code]
        print(f"获取到 {len(stock_data)} 条行情记录")
        
        # 3. 获取财务数据
        print("5. 获取财务数据...")
        try:
            financial_data = xtdata.get_financial_data([stock_code], ['Balance', 'Income', 'CashFlow'])
            print("财务数据获取成功")
        except Exception as e:
            print(f"财务数据获取失败: {e}")
            financial_data = None
        
        # 4. 获取资金流向数据
        print("6. 获取资金流向数据...")
        try:
            capital_flow_data = xtdata.get_capital_flow([stock_code])
            print("资金流向数据获取成功")
        except Exception as e:
            print(f"资金流向数据获取失败: {e}")
            capital_flow_data = None
        
        # 5. 获取指数成分股信息
        print("7. 获取指数成分股信息...")
        index_components = {}
        index_names = ['沪深300', '上证50', '中证500', '中证1000', '中证2000', '创业板指']
        
        for idx_name in index_names:
            try:
                components = xtdata.get_stock_list_in_sector(idx_name)
                index_components[idx_name] = stock_code in components if components else False
                print(f"  {idx_name}: {'是' if index_components[idx_name] else '否'}")
            except Exception as e:
                print(f"  {idx_name}: 获取失败 ({e})")
                index_components[idx_name] = False
        
        # 6. 获取行业分类信息
        print("8. 获取行业分类信息...")
        try:
            industry_info = xtdata.get_instrument_detail(stock_code)
            if industry_info:
                sw_industry_l1 = industry_info.get('IndustryName1', '')
                sw_industry_l2 = industry_info.get('IndustryName2', '')
                sw_industry_l3 = industry_info.get('IndustryName3', '')
                print(f"  行业分类: {sw_industry_l1} > {sw_industry_l2} > {sw_industry_l3}")
            else:
                sw_industry_l1 = sw_industry_l2 = sw_industry_l3 = ''
        except Exception as e:
            print(f"行业信息获取失败: {e}")
            sw_industry_l1 = sw_industry_l2 = sw_industry_l3 = ''
        
        # 7. 获取分时数据（可选，比较复杂）
        print("9. 获取分时数据...")
        print("  注意：分时数据获取较复杂，此版本使用占位符")
        
        # 8. 整合数据
        print("10. 整合数据...")
        result_data = []
        
        for trade_date, row in stock_data.iterrows():
            date_str = trade_date.strftime('%Y%m%d')
            
            # 基础行情数据
            data_row = {
                '股票代码': stock_code,
                '股票名称': '格力电器',
                '交易日期': date_str,
                '开盘价': row.get('open', 0),
                '最高价': row.get('high', 0),
                '最低价': row.get('low', 0),
                '收盘价': row.get('close', 0),
                '前收盘价': row.get('preClose', 0),
                '成交量': row.get('volume', 0),
                '成交额': row.get('amount', 0),
                '流通市值': 0,  # 需要根据实际API调整
                '总市值': 0,   # 需要根据实际API调整
            }
            
            # 财务数据（需要根据实际返回的数据结构调整）
            if financial_data and stock_code in financial_data:
                fin_data = financial_data[stock_code]
                data_row.update({
                    '净利润TTM': fin_data.get('net_profit_ttm', 0),
                    '现金流TTM': fin_data.get('cash_flow_ttm', 0),
                    '净资产': fin_data.get('total_equity', 0),
                    '总资产': fin_data.get('total_assets', 0),
                    '总负债': fin_data.get('total_liabilities', 0),
                    '净利润(当季)': fin_data.get('net_profit_quarter', 0),
                })
            else:
                data_row.update({
                    '净利润TTM': 0, '现金流TTM': 0, '净资产': 0,
                    '总资产': 0, '总负债': 0, '净利润(当季)': 0,
                })
            
            # 资金流向数据（需要根据实际返回的数据结构调整）
            if capital_flow_data and date_str in capital_flow_data:
                flow_data = capital_flow_data[date_str]
                data_row.update({
                    '中户资金买入额': flow_data.get('medium_buy', 0),
                    '中户资金卖出额': flow_data.get('medium_sell', 0),
                    '大户资金买入额': flow_data.get('large_buy', 0),
                    '大户资金卖出额': flow_data.get('large_sell', 0),
                    '散户资金买入额': flow_data.get('retail_buy', 0),
                    '散户资金卖出额': flow_data.get('retail_sell', 0),
                    '机构资金买入额': flow_data.get('institution_buy', 0),
                    '机构资金卖出额': flow_data.get('institution_sell', 0),
                })
            else:
                data_row.update({
                    '中户资金买入额': 0, '中户资金卖出额': 0,
                    '大户资金买入额': 0, '大户资金卖出额': 0,
                    '散户资金买入额': 0, '散户资金卖出额': 0,
                    '机构资金买入额': 0, '机构资金卖出额': 0,
                })
            
            # 指数成分股信息
            data_row.update({
                '沪深300成分股': 1 if index_components.get('沪深300', False) else 0,
                '上证50成分股': 1 if index_components.get('上证50', False) else 0,
                '中证500成分股': 1 if index_components.get('中证500', False) else 0,
                '中证1000成分股': 1 if index_components.get('中证1000', False) else 0,
                '中证2000成分股': 1 if index_components.get('中证2000', False) else 0,
                '创业板指成分股': 1 if index_components.get('创业板指', False) else 0,
            })
            
            # 行业分类
            data_row.update({
                '新版申万一级行业名称': sw_industry_l1,
                '新版申万二级行业名称': sw_industry_l2,
                '新版申万三级行业名称': sw_industry_l3,
            })
            
            # 分时价格（占位符）
            data_row.update({
                '09:35收盘价': 0,
                '09:45收盘价': 0,
                '09:55收盘价': 0,
            })
            
            result_data.append(data_row)
        
        # 9. 保存数据
        df = pd.DataFrame(result_data)
        output_file = f"格力电器_完整数据_{start_date}_{end_date}.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print("=" * 50)
        print(f"数据获取完成！")
        print(f"共获取 {len(df)} 条记录")
        print(f"数据已保存到: {output_file}")
        
        # 显示前几行数据
        print("\n前3行数据预览:")
        print(df.head(3).to_string())
        
        return df
        
    except Exception as e:
        print(f"获取数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("=" * 60)
    print("格力电器完整数据获取工具")
    print("=" * 60)
    print("使用前请确保：")
    print("1. MiniQMT客户端已启动")
    print("2. 行情连接正常")
    print("3. 具有相应的数据权限")
    print("=" * 60)
    print()
    
    # 执行数据获取
    data = get_complete_gree_data()
    
    if data is not None:
        print("\n" + "=" * 60)
        print("数据获取成功！")
        print("注意：某些字段可能需要根据实际API返回结构进行调整")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("数据获取失败！")
        print("请检查MiniQMT连接状态和数据权限")
        print("=" * 60)
