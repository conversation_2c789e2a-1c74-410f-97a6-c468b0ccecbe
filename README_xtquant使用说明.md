# XtQuant 格力电器数据获取说明

## 问题诊断

根据运行结果，出现了 `"not authenticated"` 错误，这表明：

1. **MiniQMT客户端未运行**：xtquant需要MiniQMT客户端作为数据源
2. **未连接到行情服务器**：需要在MiniQMT中连接到行情服务器
3. **认证失败**：可能需要登录账号或配置token

## 解决方案

### 1. 启动MiniQMT客户端

1. 找到并启动MiniQMT客户端程序
2. 确保客户端正常连接到行情服务器
3. 检查行情连接状态是否正常

### 2. 配置认证（如果需要VIP服务）

如果需要使用VIP服务器，需要配置token：

```python
from xtquant import xtdatacenter as xtdc

# 设置token（从投研用户中心获取）
xtdc.set_token('你的token')

# 设置VIP服务器连接池
addr_list = [
    '**************:55310', 
    '**************:55310', 
    '*************:55300',
    '*************:55300',
    '***********:55300',
    '***********:55300'
]
xtdc.set_allow_optmize_address(addr_list)

# 初始化
xtdc.init()
port = xtdc.listen(port=58621)
xtdata.connect(port=port)
```

### 3. 数据获取流程

正确的数据获取流程应该是：

```python
from xtquant import xtdata

# 1. 下载历史数据到本地
stock_code = "000651.SZ"
xtdata.download_history_data(stock_code, period='1d', start_time='20250128', end_time='20250705')

# 2. 下载财务数据
xtdata.download_financial_data([stock_code])

# 3. 下载板块数据
xtdata.download_sector_data()

# 4. 获取数据
data = xtdata.get_market_data_ex(['open', 'high', 'low', 'close', 'volume', 'amount'], 
                                [stock_code], period='1d', start_time='20250128', end_time='20250705')
```

## 所需数据字段说明

您需要的数据字段及对应的xtquant获取方法：

### 基础行情数据
- 股票代码、股票名称、交易日期、开盘价、最高价、最低价、收盘价、前收盘价、成交量、成交额
- 使用：`xtdata.get_market_data_ex()`

### 市值数据
- 流通市值、总市值
- 使用：`xtdata.get_market_data_ex()` 中的相关字段

### 财务数据
- 净利润TTM、现金流TTM、净资产、总资产、总负债、净利润(当季)
- 使用：`xtdata.get_financial_data()`

### 资金流向数据
- 中户资金买入额/卖出额、大户资金买入额/卖出额、散户资金买入额/卖出额、机构资金买入额/卖出额
- 使用：`xtdata.get_capital_flow()` 或相关接口

### 指数成分股
- 沪深300成分股、上证50成分股、中证500成分股、中证1000成分股、中证2000成分股、创业板指成分股
- 使用：`xtdata.get_stock_list_in_sector()`

### 行业分类
- 新版申万一级行业名称、新版申万二级行业名称、新版申万三级行业名称
- 使用：`xtdata.get_instrument_detail()`

### 分时数据
- 09:35收盘价、09:45收盘价、09:55收盘价
- 使用：`xtdata.get_market_data_ex()` 获取分钟数据

## 下一步操作

1. **启动MiniQMT客户端**并确保连接正常
2. **运行提供的脚本**重新尝试获取数据
3. **如果仍有问题**，检查是否需要VIP权限或特殊配置
4. **根据实际获取的数据结构**调整脚本中的字段映射

## 注意事项

- xtquant是基于MiniQMT的Python接口，必须配合MiniQMT使用
- 某些高级数据（如资金流向、财务数据）可能需要VIP权限
- 分时数据需要实时订阅或历史分时数据权限
- 建议先测试基础行情数据，再逐步添加其他数据字段
