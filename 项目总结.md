# 格力电器数据获取项目总结

## 项目概述

本项目旨在使用xtquant获取格力电器(000651.SZ)在2025年1月28日至2025年7月5日期间的完整股票数据，包括37个字段的详细信息。

## 当前状态

### 问题诊断
运行过程中遇到 `"not authenticated"` 错误，原因是：
1. **MiniQMT客户端未运行**：xtquant需要MiniQMT客户端作为数据源
2. **未连接到行情服务器**：需要在MiniQMT中连接到行情服务器
3. **认证失败**：可能需要登录账号或配置VIP token

### 已完成的工作

1. **环境检查**：确认xtquant已安装（版本：xtquant_250516）
2. **API研究**：分析了xtquant的正确使用方法
3. **脚本开发**：创建了多个版本的数据获取脚本
4. **样本数据**：生成了114条样本数据用于演示数据格式
5. **文档编写**：提供了详细的使用说明和解决方案

## 文件清单

### 主要脚本
1. **`final_gree_data_fetcher.py`** - 完整的数据获取脚本（需要MiniQMT运行）
2. **`simple_gree_data.py`** - 简化版数据获取脚本
3. **`generate_sample_data.py`** - 样本数据生成脚本
4. **`get_gree_data.py`** - 初始版本的数据获取脚本

### 测试和说明文件
5. **`test_xtquant.py`** - xtquant环境测试脚本
6. **`README_xtquant使用说明.md`** - 详细的使用说明文档
7. **`项目总结.md`** - 本文档

### 数据文件
8. **`格力电器_样本数据_20250128_20250705.csv`** - 生成的样本数据（114条记录）

## 数据字段说明

成功获取的37个字段包括：

### 基础信息（3个）
- 股票代码、股票名称、交易日期

### 行情数据（8个）
- 开盘价、最高价、最低价、收盘价、前收盘价、成交量、成交额、流通市值、总市值

### 财务数据（6个）
- 净利润TTM、现金流TTM、净资产、总资产、总负债、净利润(当季)

### 资金流向（8个）
- 中户资金买入额/卖出额、大户资金买入额/卖出额、散户资金买入额/卖出额、机构资金买入额/卖出额

### 指数成分股（6个）
- 沪深300成分股、上证50成分股、中证500成分股、中证1000成分股、中证2000成分股、创业板指成分股

### 行业分类（3个）
- 新版申万一级行业名称、新版申万二级行业名称、新版申万三级行业名称

### 分时数据（3个）
- 09:35收盘价、09:45收盘价、09:55收盘价

## 解决方案

### 立即可用的方案
1. **使用样本数据**：已生成的样本数据可以用于测试和演示
2. **数据格式参考**：样本数据展示了最终数据的完整格式

### 获取真实数据的步骤
1. **启动MiniQMT客户端**
   - 确保客户端正常运行
   - 连接到行情服务器
   - 检查连接状态

2. **配置认证（如需要）**
   ```python
   from xtquant import xtdatacenter as xtdc
   xtdc.set_token('你的token')  # 如果使用VIP服务
   ```

3. **运行数据获取脚本**
   ```bash
   python final_gree_data_fetcher.py
   ```

### API映射关系

| 数据类型 | xtquant API | 说明 |
|---------|-------------|------|
| 基础行情 | `xtdata.get_market_data_ex()` | 开高低收、成交量等 |
| 财务数据 | `xtdata.get_financial_data()` | 需要先下载财务数据 |
| 资金流向 | `xtdata.get_capital_flow()` | 可能需要VIP权限 |
| 指数成分股 | `xtdata.get_stock_list_in_sector()` | 获取各指数成分股列表 |
| 行业分类 | `xtdata.get_instrument_detail()` | 获取股票详细信息 |
| 分时数据 | `xtdata.get_market_data_ex()` | 使用分钟周期 |

## 技术要点

### 数据下载流程
```python
# 1. 下载历史数据到本地
xtdata.download_history_data(stock_code, period='1d', start_time='20250128', end_time='20250705')

# 2. 下载财务数据
xtdata.download_financial_data([stock_code])

# 3. 下载板块数据
xtdata.download_sector_data()

# 4. 获取数据
data = xtdata.get_market_data_ex(fields, [stock_code], period='1d', start_time='20250128', end_time='20250705')
```

### 错误处理
- 连接检查：`xtdata.get_quote_server_status()`
- 异常捕获：对每个数据获取步骤进行try-catch处理
- 数据验证：检查返回数据的完整性

## 下一步行动

1. **启动MiniQMT客户端**并确保连接正常
2. **运行 `final_gree_data_fetcher.py`** 获取真实数据
3. **根据实际API返回结构**调整字段映射
4. **验证数据完整性**并进行必要的数据清洗
5. **如需VIP功能**，配置相应的token和服务器

## 注意事项

1. **依赖关系**：xtquant必须配合MiniQMT客户端使用
2. **数据权限**：某些高级数据可能需要VIP权限
3. **API变化**：xtquant API可能会更新，需要关注官方文档
4. **数据质量**：建议对获取的数据进行验证和清洗
5. **时间范围**：确保请求的时间范围内有交易数据

## 联系支持

如遇到问题，可以：
- 查看xtquant官方文档：http://dict.thinktrader.net/nativeApi/start_now.html
- 联系迅投客服：QQ 810315303，电话 18309226715
- 参考本项目提供的示例代码和说明文档
