#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速获取格力电器基础数据
先获取基础行情数据，其他数据逐步添加
"""

from xtquant import xtdata
import pandas as pd
from datetime import datetime

def get_basic_gree_data():
    """获取格力电器基础数据"""
    
    # 格力电器股票代码
    stock_code = "000651.SZ"
    start_date = "20250128"
    end_date = "20250705"
    
    print(f"开始获取格力电器({stock_code})基础数据...")
    print(f"时间范围: {start_date} - {end_date}")
    print("=" * 50)
    
    try:
        # 1. 先尝试下载历史行情数据
        print("1. 下载历史行情数据...")
        try:
            xtdata.download_history_data(stock_code, period='1d', start_time=start_date, end_time=end_date)
            print("   历史行情数据下载完成")
        except Exception as e:
            print(f"   历史行情数据下载失败: {e}")
            print("   尝试获取已有数据...")
        
        # 2. 获取基础行情数据
        print("2. 获取基础行情数据...")
        field_list = ['open', 'high', 'low', 'close', 'preClose', 'volume', 'amount']
        try:
            basic_data = xtdata.get_market_data_ex(field_list, [stock_code], period='1d', 
                                                 start_time=start_date, end_time=end_date)
            if basic_data and stock_code in basic_data:
                stock_data = basic_data[stock_code]
                print(f"   成功获取 {len(stock_data)} 条行情记录")
            else:
                print("   未获取到指定时间范围的数据，尝试获取所有可用数据...")
                basic_data = xtdata.get_market_data_ex(field_list, [stock_code], period='1d')
                if basic_data and stock_code in basic_data:
                    stock_data = basic_data[stock_code]
                    print(f"   获取到 {len(stock_data)} 条历史记录")
                    
                    # 筛选指定时间范围
                    start_dt = pd.to_datetime(start_date, format='%Y%m%d')
                    end_dt = pd.to_datetime(end_date, format='%Y%m%d')
                    stock_data = stock_data[(stock_data.index >= start_dt) & (stock_data.index <= end_dt)]
                    print(f"   筛选后得到 {len(stock_data)} 条记录")
                else:
                    print("   未获取到任何行情数据")
                    return None
        except Exception as e:
            print(f"   获取行情数据失败: {e}")
            return None
        
        if len(stock_data) == 0:
            print("指定时间范围内没有数据")
            return None
        
        # 3. 获取股票基本信息
        print("3. 获取股票基本信息...")
        try:
            instrument_detail = xtdata.get_instrument_detail(stock_code)
            if instrument_detail:
                stock_name = instrument_detail.get('InstrumentName', '格力电器')
                print(f"   股票名称: {stock_name}")
            else:
                stock_name = '格力电器'
                print("   使用默认股票名称")
        except Exception as e:
            print(f"   获取股票信息失败: {e}")
            stock_name = '格力电器'
        
        # 4. 获取指数成分股信息
        print("4. 获取指数成分股信息...")
        index_components = {}
        index_names = ['沪深300', '上证50', '中证500']  # 先测试主要指数
        
        for idx_name in index_names:
            try:
                components = xtdata.get_stock_list_in_sector(idx_name)
                if components:
                    index_components[idx_name] = stock_code in components
                    print(f"   {idx_name}: {'是' if index_components[idx_name] else '否'}")
                else:
                    index_components[idx_name] = False
                    print(f"   {idx_name}: 未获取到成分股列表")
            except Exception as e:
                print(f"   {idx_name}: 获取失败 ({e})")
                index_components[idx_name] = False
        
        # 5. 整合数据
        print("5. 整合数据...")
        result_data = []
        
        for trade_date, row in stock_data.iterrows():
            # 处理日期格式
            if isinstance(trade_date, str):
                date_str = trade_date
            else:
                date_str = trade_date.strftime('%Y%m%d')
            
            # 计算前收盘价（使用preClose字段或前一日收盘价）
            pre_close = row.get('preClose', 0)
            if pre_close == 0 and len(result_data) > 0:
                pre_close = result_data[-1]['收盘价']
            
            data_row = {
                '股票代码': stock_code,
                '股票名称': stock_name,
                '交易日期': date_str,
                '开盘价': round(row.get('open', 0), 2),
                '最高价': round(row.get('high', 0), 2),
                '最低价': round(row.get('low', 0), 2),
                '收盘价': round(row.get('close', 0), 2),
                '前收盘价': round(pre_close, 2),
                '成交量': int(row.get('volume', 0)),
                '成交额': round(row.get('amount', 0), 2),
                '流通市值': 0,  # 待补充
                '总市值': 0,   # 待补充
                '净利润TTM': 0,
                '现金流TTM': 0,
                '净资产': 0,
                '总资产': 0,
                '总负债': 0,
                '净利润(当季)': 0,
                '中户资金买入额': 0,
                '中户资金卖出额': 0,
                '大户资金买入额': 0,
                '大户资金卖出额': 0,
                '散户资金买入额': 0,
                '散户资金卖出额': 0,
                '机构资金买入额': 0,
                '机构资金卖出额': 0,
                '沪深300成分股': 1 if index_components.get('沪深300', False) else 0,
                '上证50成分股': 1 if index_components.get('上证50', False) else 0,
                '中证500成分股': 1 if index_components.get('中证500', False) else 0,
                '中证1000成分股': 0,
                '中证2000成分股': 0,
                '创业板指成分股': 0,
                '新版申万一级行业名称': '',
                '新版申万二级行业名称': '',
                '新版申万三级行业名称': '',
                '09:35收盘价': 0,
                '09:45收盘价': 0,
                '09:55收盘价': 0,
            }
            
            result_data.append(data_row)
        
        # 6. 保存数据
        df = pd.DataFrame(result_data)
        output_file = f"格力电器_基础数据_{start_date}_{end_date}.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print("=" * 50)
        print(f"基础数据获取完成！")
        print(f"共获取 {len(df)} 条记录")
        print(f"数据已保存到: {output_file}")
        
        # 显示数据概览
        print(f"\n数据概览:")
        print(f"时间范围: {df['交易日期'].min()} - {df['交易日期'].max()}")
        print(f"价格范围: {df['收盘价'].min():.2f} - {df['收盘价'].max():.2f}")
        print(f"平均成交量: {df['成交量'].mean():,.0f}")
        print(f"平均成交额: {df['成交额'].mean():,.0f}")
        
        # 显示前几行数据
        print("\n前5行数据预览:")
        display_cols = ['股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '成交量', '成交额']
        print(df[display_cols].head().to_string(index=False))
        
        return df
        
    except Exception as e:
        print(f"获取数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("=" * 60)
    print("格力电器基础数据获取工具")
    print("=" * 60)
    print("说明：先获取基础行情数据，其他数据字段后续补充")
    print("=" * 60)
    print()
    
    # 执行数据获取
    data = get_basic_gree_data()
    
    if data is not None:
        print("\n" + "=" * 60)
        print("基础数据获取成功！")
        print("注意：财务数据、资金流向等字段需要进一步配置")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("数据获取失败！")
        print("=" * 60)
