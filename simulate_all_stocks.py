#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模拟所有股票数据生成工具
基于格力电器的真实数据，生成多只股票的完整数据
"""

import pandas as pd
import numpy as np
from datetime import datetime
import random

def load_gree_data():
    """加载格力电器的真实数据作为模板"""
    try:
        df = pd.read_csv("格力电器_最终完整数据_20250128_20250705.csv", encoding='utf-8-sig')
        print(f"成功加载格力电器数据: {len(df)} 条记录")
        return df
    except FileNotFoundError:
        print("未找到格力电器数据文件，请先运行格力电器数据获取脚本")
        return None

def get_stock_list():
    """获取要模拟的股票列表"""
    stocks = [
        # 银行股
        ("000001.SZ", "平安银行", "金融业", "银行", "股份制银行", True, False),
        ("600036.SH", "招商银行", "金融业", "银行", "股份制银行", True, True),
        ("601398.SH", "工商银行", "金融业", "银行", "国有银行", True, True),
        ("601939.SH", "建设银行", "金融业", "银行", "国有银行", True, True),
        
        # 白酒股
        ("600519.SH", "贵州茅台", "食品饮料", "白酒", "白酒", True, True),
        ("000858.SZ", "五粮液", "食品饮料", "白酒", "白酒", True, False),
        ("000568.SZ", "泸州老窖", "食品饮料", "白酒", "白酒", True, False),
        
        # 家电股
        ("000651.SZ", "格力电器", "家用电器", "白色家电", "空调", True, False),
        ("000333.SZ", "美的集团", "家用电器", "白色家电", "综合家电", True, False),
        ("600690.SH", "海尔智家", "家用电器", "白色家电", "洗衣机", True, False),
        
        # 科技股
        ("002415.SZ", "海康威视", "电子", "安防设备", "视频监控", True, False),
        ("300059.SZ", "东方财富", "金融业", "证券", "证券", False, False),
        ("300750.SZ", "宁德时代", "电子", "电池", "锂电池", False, False),
        
        # 汽车股
        ("002594.SZ", "比亚迪", "汽车", "新能源汽车", "新能源汽车", False, False),
        ("601633.SH", "长城汽车", "汽车", "乘用车", "SUV", True, False),
        
        # 地产股
        ("000002.SZ", "万科A", "房地产业", "房地产开发", "住宅开发", True, False),
        ("000001.SZ", "平安银行", "金融业", "银行", "股份制银行", True, False),
        
        # 保险股
        ("601318.SH", "中国平安", "金融业", "保险", "寿险", True, True),
        ("601601.SH", "中国太保", "金融业", "保险", "寿险", True, True),
        
        # 医药股
        ("000661.SZ", "长春高新", "医药生物", "生物制品", "疫苗", False, False),
        ("300015.SZ", "爱尔眼科", "医药生物", "医疗服务", "专科医院", False, False),
    ]
    
    return stocks

def generate_stock_data(base_data, stock_info):
    """基于基础数据生成单只股票的数据"""
    stock_code, stock_name, industry_l1, industry_l2, industry_l3, is_hs300, is_sz50 = stock_info
    
    # 复制基础数据
    stock_data = base_data.copy()
    
    # 修改股票代码和名称
    stock_data['股票代码'] = stock_code
    stock_data['股票名称'] = stock_name
    
    # 修改行业信息
    stock_data['新版申万一级行业名称'] = industry_l1
    stock_data['新版申万二级行业名称'] = industry_l2
    stock_data['新版申万三级行业名称'] = industry_l3
    
    # 修改指数成分股
    stock_data['沪深300成分股'] = 1 if is_hs300 else 0
    stock_data['上证50成分股'] = 1 if is_sz50 else 0
    stock_data['创业板指成分股'] = 1 if stock_code.startswith('300') else 0
    
    # 根据股票类型调整价格范围
    price_multiplier = get_price_multiplier(stock_code, stock_name)
    
    # 调整价格相关字段
    price_fields = ['开盘价', '最高价', '最低价', '收盘价', '前收盘价', '09:35收盘价', '09:45收盘价', '09:55收盘价']
    for field in price_fields:
        stock_data[field] = stock_data[field] * price_multiplier
        stock_data[field] = stock_data[field].round(2)
    
    # 调整成交量（根据股票特性）
    volume_multiplier = get_volume_multiplier(stock_code, stock_name)
    stock_data['成交量'] = (stock_data['成交量'] * volume_multiplier).astype(int)
    
    # 重新计算成交额
    stock_data['成交额'] = stock_data['成交量'] * stock_data['收盘价']
    
    # 调整股本和市值
    total_shares, float_shares = get_shares_info(stock_code, stock_name)
    stock_data['总市值'] = stock_data['收盘价'] * total_shares
    stock_data['流通市值'] = stock_data['收盘价'] * float_shares
    
    # 调整财务数据（基于市值）
    market_cap_ratio = stock_data['总市值'] / stock_data['总市值'].iloc[0]  # 相对于第一天的市值比例
    base_net_profit = stock_data['净利润TTM'].iloc[0]
    
    stock_data['净利润TTM'] = base_net_profit * market_cap_ratio * get_profit_multiplier(stock_code)
    stock_data['现金流TTM'] = stock_data['净利润TTM'] * 1.2
    stock_data['净资产'] = stock_data['总市值'] * 0.4
    stock_data['总资产'] = stock_data['净资产'] * 2
    stock_data['总负债'] = stock_data['总资产'] - stock_data['净资产']
    stock_data['净利润(当季)'] = stock_data['净利润TTM'] / 4
    
    # 调整资金流向（基于成交额）
    flow_ratio = stock_data['成交额'] / stock_data['成交额'].iloc[0]
    base_retail_buy = stock_data['散户资金买入额'].iloc[0]
    
    stock_data['散户资金买入额'] = base_retail_buy * flow_ratio
    stock_data['散户资金卖出额'] = stock_data['散户资金买入额']
    stock_data['中户资金买入额'] = stock_data['散户资金买入额'] * 0.5
    stock_data['中户资金卖出额'] = stock_data['中户资金买入额']
    stock_data['大户资金买入额'] = stock_data['散户资金买入额'] * 0.375
    stock_data['大户资金卖出额'] = stock_data['大户资金买入额']
    stock_data['机构资金买入额'] = stock_data['散户资金买入额'] * 0.25
    stock_data['机构资金卖出额'] = stock_data['机构资金买入额']
    
    # 四舍五入财务数据
    financial_fields = ['净利润TTM', '现金流TTM', '净资产', '总资产', '总负债', '净利润(当季)',
                       '散户资金买入额', '散户资金卖出额', '中户资金买入额', '中户资金卖出额',
                       '大户资金买入额', '大户资金卖出额', '机构资金买入额', '机构资金卖出额',
                       '总市值', '流通市值', '成交额']
    
    for field in financial_fields:
        stock_data[field] = stock_data[field].round(2)
    
    return stock_data

def get_price_multiplier(stock_code, stock_name):
    """根据股票类型获取价格倍数"""
    if "茅台" in stock_name:
        return 40.0  # 茅台价格较高
    elif "银行" in stock_name or stock_code.startswith('60'):
        return random.uniform(0.3, 0.8)  # 银行股价格较低
    elif "保险" in stock_name:
        return random.uniform(1.2, 2.0)  # 保险股
    elif "科技" in stock_name or stock_code.startswith('300'):
        return random.uniform(1.5, 3.0)  # 科技股价格较高
    else:
        return random.uniform(0.8, 1.5)  # 其他股票

def get_volume_multiplier(stock_code, stock_name):
    """根据股票类型获取成交量倍数"""
    if "银行" in stock_name:
        return random.uniform(2.0, 5.0)  # 银行股成交量大
    elif "茅台" in stock_name:
        return random.uniform(0.3, 0.6)  # 茅台成交量相对较小
    elif stock_code.startswith('300'):
        return random.uniform(0.8, 1.5)  # 创业板
    else:
        return random.uniform(0.7, 2.0)

def get_shares_info(stock_code, stock_name):
    """获取股本信息"""
    shares_map = {
        "平安银行": (19400000000, 19400000000),
        "招商银行": (25200000000, 25200000000),
        "工商银行": (35600000000, 35600000000),
        "建设银行": (25000000000, 25000000000),
        "贵州茅台": (1256000000, 1256000000),
        "五粮液": (3870000000, 3870000000),
        "格力电器": (6000000000, 5500000000),
        "美的集团": (7000000000, 6500000000),
        "中国平安": (18300000000, 18300000000),
        "万科A": (11000000000, 11000000000),
    }
    
    return shares_map.get(stock_name, (1000000000, 800000000))

def get_profit_multiplier(stock_code):
    """获取利润倍数"""
    if stock_code.startswith('60'):
        return random.uniform(0.8, 1.5)  # 银行等传统行业
    elif stock_code.startswith('300'):
        return random.uniform(0.3, 0.8)  # 科技股利润率可能较低
    else:
        return random.uniform(0.6, 1.2)

def generate_all_stocks_data():
    """生成所有股票的数据"""
    print("=" * 70)
    print("模拟所有股票数据生成工具")
    print("=" * 70)
    
    # 1. 加载格力电器数据作为模板
    base_data = load_gree_data()
    if base_data is None:
        return None
    
    # 2. 获取股票列表
    stock_list = get_stock_list()
    print(f"准备生成 {len(stock_list)} 只股票的数据")
    
    # 3. 生成所有股票数据
    all_data = []
    
    for i, stock_info in enumerate(stock_list, 1):
        stock_code, stock_name = stock_info[0], stock_info[1]
        print(f"进度 {i}/{len(stock_list)}: 生成 {stock_code} {stock_name}")
        
        stock_data = generate_stock_data(base_data, stock_info)
        all_data.append(stock_data)
    
    # 4. 合并所有数据
    final_df = pd.concat(all_data, ignore_index=True)
    
    # 5. 保存数据
    timestamp = datetime.now().strftime('%Y%m%d_%H%M')
    output_file = f"模拟所有股票完整数据_{timestamp}.csv"
    final_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print("=" * 70)
    print("数据生成完成！")
    print(f"股票数量: {len(stock_list)}")
    print(f"总记录数: {len(final_df)}")
    print(f"文件保存为: {output_file}")
    
    # 数据概览
    print(f"\n数据概览:")
    print(f"时间范围: {final_df['交易日期'].min()} - {final_df['交易日期'].max()}")
    print(f"平均每股记录数: {len(final_df) / len(stock_list):.1f}")
    
    # 显示股票列表
    print(f"\n生成的股票列表:")
    stock_summary = final_df.groupby(['股票代码', '股票名称']).size().reset_index(name='记录数')
    for _, row in stock_summary.iterrows():
        print(f"  {row['股票代码']} {row['股票名称']}: {row['记录数']} 条")
    
    # 行业分布
    print(f"\n行业分布:")
    industry_dist = final_df['新版申万一级行业名称'].value_counts()
    for industry, count in industry_dist.items():
        print(f"  {industry}: {count} 条记录")
    
    # 指数成分股统计
    print(f"\n指数成分股统计:")
    hs300_count = final_df[final_df['沪深300成分股'] == 1]['股票代码'].nunique()
    sz50_count = final_df[final_df['上证50成分股'] == 1]['股票代码'].nunique()
    cyb_count = final_df[final_df['创业板指成分股'] == 1]['股票代码'].nunique()
    print(f"  沪深300成分股: {hs300_count} 只")
    print(f"  上证50成分股: {sz50_count} 只")
    print(f"  创业板指成分股: {cyb_count} 只")
    
    print("=" * 70)
    return final_df

if __name__ == "__main__":
    # 执行数据生成
    result = generate_all_stocks_data()
    
    if result is not None:
        print("🎉 所有股票数据生成成功！")
        print("基于格力电器真实数据，生成了多只股票的完整37字段数据")
    else:
        print("❌ 数据生成失败！")
