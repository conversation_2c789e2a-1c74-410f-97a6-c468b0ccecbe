#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版格力电器股票数据获取脚本
"""

from xtquant import xtdata
import pandas as pd

def get_simple_gree_data():
    """获取格力电器基础数据"""
    
    # 格力电器股票代码
    stock_code = "000651.SZ"
    
    print(f"开始获取格力电器({stock_code})数据...")
    
    try:
        # 检查连接状态
        print("检查xtdata连接状态...")
        
        # 尝试获取最新行情
        print("尝试获取最新行情...")
        try:
            full_tick = xtdata.get_full_tick([stock_code])
            if full_tick:
                print("成功获取最新行情:")
                print(full_tick)
            else:
                print("未获取到最新行情")
        except Exception as e:
            print(f"获取最新行情失败: {e}")
        
        # 尝试获取已有的历史数据
        print("尝试获取已有的历史数据...")
        try:
            # 不指定时间范围，获取已有数据
            data = xtdata.get_market_data_ex(['open', 'high', 'low', 'close', 'volume'], [stock_code], period='1d')
            if data and stock_code in data:
                stock_data = data[stock_code]
                print(f"成功获取历史数据，共{len(stock_data)}条记录")
                print("最近5条数据:")
                print(stock_data.tail())
                
                # 创建基础数据表
                result_data = []
                for trade_date, row in stock_data.iterrows():
                    data_row = {
                        '股票代码': stock_code,
                        '股票名称': '格力电器',
                        '交易日期': trade_date.strftime('%Y%m%d'),
                        '开盘价': row.get('open', 0),
                        '最高价': row.get('high', 0),
                        '最低价': row.get('low', 0),
                        '收盘价': row.get('close', 0),
                        '前收盘价': 0,  # 需要计算
                        '成交量': row.get('volume', 0),
                        '成交额': 0,  # 需要获取amount字段
                        '流通市值': 0,
                        '总市值': 0,
                        '净利润TTM': 0,
                        '现金流TTM': 0,
                        '净资产': 0,
                        '总资产': 0,
                        '总负债': 0,
                        '净利润(当季)': 0,
                        '中户资金买入额': 0,
                        '中户资金卖出额': 0,
                        '大户资金买入额': 0,
                        '大户资金卖出额': 0,
                        '散户资金买入额': 0,
                        '散户资金卖出额': 0,
                        '机构资金买入额': 0,
                        '机构资金卖出额': 0,
                        '沪深300成分股': 0,
                        '上证50成分股': 0,
                        '中证500成分股': 0,
                        '中证1000成分股': 0,
                        '中证2000成分股': 0,
                        '创业板指成分股': 0,
                        '新版申万一级行业名称': '',
                        '新版申万二级行业名称': '',
                        '新版申万三级行业名称': '',
                        '09:35收盘价': 0,
                        '09:45收盘价': 0,
                        '09:55收盘价': 0,
                    }
                    result_data.append(data_row)
                
                # 转换为DataFrame并保存
                df = pd.DataFrame(result_data)
                
                # 筛选指定时间范围的数据
                df['交易日期_dt'] = pd.to_datetime(df['交易日期'], format='%Y%m%d')
                start_date = pd.to_datetime('20250128', format='%Y%m%d')
                end_date = pd.to_datetime('20250705', format='%Y%m%d')
                
                filtered_df = df[(df['交易日期_dt'] >= start_date) & (df['交易日期_dt'] <= end_date)]
                filtered_df = filtered_df.drop('交易日期_dt', axis=1)
                
                if len(filtered_df) > 0:
                    output_file = "格力电器_基础数据_20250128_20250705.csv"
                    filtered_df.to_csv(output_file, index=False, encoding='utf-8-sig')
                    print(f"数据已保存到: {output_file}")
                    print(f"共保存 {len(filtered_df)} 条记录")
                    
                    # 显示前几行
                    print("\n前5行数据:")
                    print(filtered_df.head())
                    
                    return filtered_df
                else:
                    print("指定时间范围内没有数据")
                    return None
                    
            else:
                print("未获取到历史数据")
                return None
                
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return None
            
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("=" * 50)
    print("格力电器数据获取工具")
    print("=" * 50)
    
    # 提示用户
    print("注意：")
    print("1. 请确保MiniQMT客户端已启动并连接到行情服务器")
    print("2. 如果出现认证错误，请检查MiniQMT的连接状态")
    print("3. 本脚本获取基础行情数据，其他数据字段需要额外配置")
    print()
    
    data = get_simple_gree_data()
    
    if data is not None:
        print("\n数据获取成功！")
        print("注意：部分字段（如财务数据、资金流向等）需要额外的API调用和权限")
    else:
        print("\n数据获取失败！")
        print("请检查：")
        print("1. MiniQMT客户端是否正常运行")
        print("2. 行情连接是否正常")
        print("3. 是否有相应的数据权限")
