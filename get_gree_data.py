#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取格力电器股票数据脚本
时间范围: 20250128-20250705
"""

from xtquant import xtdata
import pandas as pd
from datetime import datetime
import time

def get_gree_stock_data():
    """获取格力电器股票数据"""

    # 格力电器股票代码
    stock_code = "000651.SZ"

    # 时间范围
    start_date = "20250128"
    end_date = "20250705"

    print(f"开始获取格力电器({stock_code})数据...")
    print(f"时间范围: {start_date} - {end_date}")

    try:
        # 首先下载历史数据到本地
        print("下载历史行情数据...")
        xtdata.download_history_data(stock_code, period='1d', start_time=start_date, end_time=end_date)

        # 下载财务数据
        print("下载财务数据...")
        xtdata.download_financial_data([stock_code])

        # 下载板块数据
        print("下载板块数据...")
        xtdata.download_sector_data()

        # 获取基础行情数据
        print("获取基础行情数据...")
        field_list = ['open', 'high', 'low', 'close', 'preClose', 'volume', 'amount']
        basic_data = xtdata.get_market_data_ex(field_list, [stock_code], period='1d', start_time=start_date, end_time=end_date)

        if basic_data is None or len(basic_data) == 0:
            print("未获取到基础行情数据")
            return None

        # 获取财务数据
        print("获取财务数据...")
        try:
            financial_data = xtdata.get_financial_data([stock_code], ['Balance', 'Income', 'CashFlow'])
        except Exception as e:
            print(f"获取财务数据失败: {e}")
            financial_data = None

        # 获取资金流向数据
        print("获取资金流向数据...")
        try:
            capital_flow_data = xtdata.get_capital_flow([stock_code])
        except Exception as e:
            print(f"获取资金流向数据失败: {e}")
            capital_flow_data = None

        # 获取指数成分股信息
        print("获取指数成分股信息...")
        index_components = {}
        index_codes = ['沪深300', '上证50', '中证500', '中证1000', '中证2000', '创业板指']

        for idx_name in index_codes:
            try:
                components = xtdata.get_stock_list_in_sector(idx_name)
                index_components[idx_name] = stock_code in components if components else False
            except Exception as e:
                print(f"获取{idx_name}成分股失败: {e}")
                index_components[idx_name] = False

        # 获取行业分类信息
        print("获取行业分类信息...")
        try:
            industry_info = xtdata.get_instrument_detail(stock_code)
            sw_industry_l1 = industry_info.get('IndustryName1', '') if industry_info else ''
            sw_industry_l2 = industry_info.get('IndustryName2', '') if industry_info else ''
            sw_industry_l3 = industry_info.get('IndustryName3', '') if industry_info else ''
        except Exception as e:
            print(f"获取行业信息失败: {e}")
            sw_industry_l1 = sw_industry_l2 = sw_industry_l3 = ''

        # 获取分时数据（09:35, 09:45, 09:55的收盘价）
        print("获取分时数据...")
        intraday_prices = {}

        # 由于分时数据获取比较复杂，这里先用占位符
        # 实际使用中可以通过订阅实时数据或历史分时数据来获取
        print("注意：分时数据需要实时订阅或历史分时数据，这里使用占位符")
        
        # 整合数据
        print("整合数据...")
        result_data = []

        # 获取股票数据
        stock_data = basic_data[stock_code] if stock_code in basic_data else None
        if stock_data is None:
            print("未获取到股票数据")
            return None

        for trade_date, row in stock_data.iterrows():
            date_str = trade_date.strftime('%Y%m%d')

            # 基础数据
            data_row = {
                '股票代码': stock_code,
                '股票名称': '格力电器',
                '交易日期': date_str,
                '开盘价': row.get('open', 0),
                '最高价': row.get('high', 0),
                '最低价': row.get('low', 0),
                '收盘价': row.get('close', 0),
                '前收盘价': row.get('preClose', 0),
                '成交量': row.get('volume', 0),
                '成交额': row.get('amount', 0),
                '流通市值': 0,  # 需要单独计算
                '总市值': 0,   # 需要单独计算
            }

            # 财务数据（使用占位符，实际需要根据xtdata的财务数据接口调整）
            data_row.update({
                '净利润TTM': 0,
                '现金流TTM': 0,
                '净资产': 0,
                '总资产': 0,
                '总负债': 0,
                '净利润(当季)': 0,
            })

            # 资金流向数据（使用占位符，实际需要根据xtdata的资金流向接口调整）
            data_row.update({
                '中户资金买入额': 0,
                '中户资金卖出额': 0,
                '大户资金买入额': 0,
                '大户资金卖出额': 0,
                '散户资金买入额': 0,
                '散户资金卖出额': 0,
                '机构资金买入额': 0,
                '机构资金卖出额': 0,
            })

            # 指数成分股信息
            data_row.update({
                '沪深300成分股': 1 if index_components.get('沪深300', False) else 0,
                '上证50成分股': 1 if index_components.get('上证50', False) else 0,
                '中证500成分股': 1 if index_components.get('中证500', False) else 0,
                '中证1000成分股': 1 if index_components.get('中证1000', False) else 0,
                '中证2000成分股': 1 if index_components.get('中证2000', False) else 0,
                '创业板指成分股': 1 if index_components.get('创业板指', False) else 0,
            })

            # 行业分类
            data_row.update({
                '新版申万一级行业名称': sw_industry_l1,
                '新版申万二级行业名称': sw_industry_l2,
                '新版申万三级行业名称': sw_industry_l3,
            })

            # 分时价格（使用占位符）
            data_row.update({
                '09:35收盘价': 0,
                '09:45收盘价': 0,
                '09:55收盘价': 0,
            })

            result_data.append(data_row)
        
        # 转换为DataFrame
        df = pd.DataFrame(result_data)
        
        # 保存到CSV文件
        output_file = f"格力电器_股票数据_{start_date}_{end_date}.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"数据获取完成！")
        print(f"共获取 {len(df)} 条记录")
        print(f"数据已保存到: {output_file}")
        
        # 显示前几行数据
        print("\n前5行数据预览:")
        print(df.head().to_string())
        
        return df
        
    except Exception as e:
        print(f"获取数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 执行数据获取
    data = get_gree_stock_data()
    
    if data is not None:
        print("\n数据获取成功！")
    else:
        print("\n数据获取失败！")
